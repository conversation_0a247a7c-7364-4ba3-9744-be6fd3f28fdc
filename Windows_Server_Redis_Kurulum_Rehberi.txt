🎯 WINDOWS SERVER 2019 REDİS KURULUM REHBERİ
================================================================
PROJE: GymKod Pro - Production Redis Deployment
HEDEF: 1 salon 100 üye → İlerde 1000+ salon 100.000+ kullanıcı
SUNUCU: Windows Server 2019, 4GB RAM, 2 CPU
KLASÖR: C:\gymproject (mevcut)
================================================================

📋 KURULUM SEÇENEKLERİ KARŞILAŞTIRMASI
================================================================

SEÇENEK 1: DOCKER ENGINE (ÖNERİLEN) ⭐⭐⭐⭐⭐
- Dock<PERSON> Desktop değil, sadece Docker Engine
- Lightweight, production-ready
- Container management kolay
- Backup/restore basit
- Monitoring entegrasyonu mevcut

SEÇENEK 2: REDIS WINDOWS NATIVE ⭐⭐⭐
- Microsoft tarafından port edilmiş
- Windows Service olarak çalışır
- Daha az kaynak kullanır
- Konfigürasyon daha karmaşık

SEÇENEK 3: WSL2 + REDIS ⭐⭐⭐⭐
- Linux Redis native çalışır
- Performance iyi
- Windows Server 2019'da WSL2 kurulumu gerekli

ÖNERİ: Docker Engine ile başlayalım, sorun olursa Redis Windows Native'e geçeriz.

================================================================
ADIM 1: DOCKER ENGINE KURULUMU (Windows Server 2019)
================================================================

1.1) PowerShell'i Administrator olarak aç
1.2) Aşağıdaki komutları sırayla çalıştır:

# Windows Container feature'ını etkinleştir
Enable-WindowsOptionalFeature -Online -FeatureName containers -All

# Hyper-V'yi etkinleştir (gerekirse)
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# Sistem yeniden başlatılacak
Restart-Computer

1.3) Yeniden başladıktan sonra PowerShell'i tekrar Administrator olarak aç:

# Docker Engine'i indir ve kur
Invoke-WebRequest -UseBasicParsing "https://raw.githubusercontent.com/microsoft/Windows-Containers/Main/helpful_tools/Install-DockerCE/install-docker-ce.ps1" -o install-docker-ce.ps1
.\install-docker-ce.ps1

# Docker service'ini başlat
Start-Service docker

# Docker'ın çalıştığını kontrol et
docker version

# Docker'ı otomatik başlatılacak şekilde ayarla
Set-Service -Name docker -StartupType Automatic

================================================================
ADIM 2: REDIS CONTAINER KURULUMU
================================================================

2.1) C:\gymproject klasörüne git:
cd C:\gymproject

2.2) Redis konfigürasyon dosyası oluştur:
New-Item -ItemType File -Path "redis.conf" -Force

2.3) redis.conf dosyasının içeriğini düzenle:
notepad redis.conf

# Aşağıdaki içeriği kopyala:
# Redis Production Configuration for GymKod
# Windows Server 2019 - 4GB RAM, 2 CPU

# Network
bind 0.0.0.0
port 6379
protected-mode yes
requirepass GymKod2024Redis!

# Memory Management (4GB RAM için optimize)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

2.4) docker-compose.prod.yml dosyası oluştur:
New-Item -ItemType File -Path "docker-compose.prod.yml" -Force
notepad docker-compose.prod.yml

# Aşağıdaki içeriği kopyala:
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymKod2024Redis!
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "GymKod2024Redis!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge

================================================================
ADIM 3: REDIS CONTAINER'I BAŞLATMA
================================================================

3.1) Redis container'ı başlat:
docker-compose -f docker-compose.prod.yml up -d

3.2) Container'ın çalıştığını kontrol et:
docker ps

# Şu çıktıyı görmelisin:
# CONTAINER ID   IMAGE           COMMAND                  CREATED         STATUS                   PORTS                    NAMES
# xxxxxxxxxxxxx  redis:7-alpine  "docker-entrypoint.s…"  X seconds ago   Up X seconds (healthy)   0.0.0.0:6379->6379/tcp   gymkod-redis-prod

3.3) Redis bağlantısını test et:
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis!

# Redis CLI'da şu komutları çalıştır:
ping
# PONG döner

info memory
# Memory kullanımını gösterir

exit

================================================================
ADIM 4: WINDOWS FİREWALL AYARLARI
================================================================

4.1) Redis port'unu aç (sadece local network için):
New-NetFirewallRule -DisplayName "Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -Profile Domain,Private

# NOT: Public profile'a ekleme, güvenlik riski!

4.2) Firewall kuralını kontrol et:
Get-NetFirewallRule -DisplayName "Redis Server"

================================================================
ADIM 5: .NET UYGULAMASINI PRODUCTION REDİS'E BAĞLAMA
================================================================

5.1) appsettings.json'da production Redis connection string'ini güncelle:
# Mevcut:
"canlı": "localhost:6379,password=GymKod2024Redis!,abortConnect=false"

# Yeni (aynı kalacak çünkü aynı sunucuda):
"canlı": "localhost:6379,password=GymKod2024Redis!,abortConnect=false,connectRetry=3,connectTimeout=5000,syncTimeout=5000"

5.2) .NET uygulamasını production modunda çalıştır:
cd C:\gymproject\GymProjectBackend\WebAPI
dotnet run --environment canlı

5.3) Bağlantıyı test et:
# Browser'da aç:
http://localhost:5165/swagger

# Cache test endpoint'ini çağır (varsa)

================================================================
ADIM 6: MONİTORİNG VE BACKUP KURULUMU
================================================================

6.1) Redis monitoring script'i oluştur:
New-Item -ItemType File -Path "redis-monitor.ps1" -Force
notepad redis-monitor.ps1

# Aşağıdaki içeriği kopyala:
# Redis Monitoring Script for GymKod
param(
    [string]$LogPath = "C:\gymproject\logs\redis-monitor.log"
)

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$redisInfo = docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory

# Memory usage check
$memoryUsed = ($redisInfo | Select-String "used_memory_human:(.+)" | ForEach-Object { $_.Matches.Groups[1].Value }).Trim()
$memoryPeak = ($redisInfo | Select-String "used_memory_peak_human:(.+)" | ForEach-Object { $_.Matches.Groups[1].Value }).Trim()

$logEntry = "$timestamp - Memory Used: $memoryUsed, Peak: $memoryPeak"
Add-Content -Path $LogPath -Value $logEntry

# Alert if memory usage > 1.5GB
$memoryUsedBytes = ($redisInfo | Select-String "used_memory:(\d+)" | ForEach-Object { $_.Matches.Groups[1].Value })
if ([long]$memoryUsedBytes -gt 1610612736) {  # 1.5GB
    Write-Warning "Redis memory usage high: $memoryUsed"
    # Burada email/SMS alert gönderebilirsin
}

6.2) Backup script'i oluştur:
New-Item -ItemType File -Path "redis-backup.ps1" -Force
notepad redis-backup.ps1

# Aşağıdaki içeriği kopyala:
# Redis Backup Script for GymKod
param(
    [string]$BackupPath = "C:\gymproject\backups\redis"
)

$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupFile = "$BackupPath\redis-backup-$timestamp.rdb"

# Backup klasörünü oluştur
if (!(Test-Path $BackupPath)) {
    New-Item -ItemType Directory -Path $BackupPath -Force
}

# Redis BGSAVE komutu ile backup al
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! BGSAVE

# Backup dosyasını kopyala
Start-Sleep -Seconds 10  # BGSAVE'in tamamlanmasını bekle
docker cp gymkod-redis-prod:/data/dump.rdb $backupFile

Write-Host "Backup completed: $backupFile"

# Eski backup'ları temizle (7 günden eski)
Get-ChildItem $BackupPath -Filter "redis-backup-*.rdb" | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-7) } | Remove-Item

6.3) Task Scheduler ile otomatik backup ayarla:
# Task Scheduler'ı aç
taskschd.msc

# Yeni task oluştur:
# Name: Redis Backup
# Trigger: Daily at 02:00
# Action: Start a program
# Program: powershell.exe
# Arguments: -File "C:\gymproject\redis-backup.ps1"

================================================================
ADIM 7: PERFORMANCE OPTİMİZASYONU
================================================================

7.1) Windows Server performance ayarları:
# Virtual Memory ayarla (8GB için)
$computerSystem = Get-WmiObject -Class Win32_ComputerSystem
$totalRAM = [math]::Round($computerSystem.TotalPhysicalMemory / 1GB)
$pagingFileSize = $totalRAM * 1.5  # 1.5x RAM

# Network optimization
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled

7.2) Docker resource limits kontrol et:
docker stats gymkod-redis-prod

# Memory ve CPU kullanımını izle

================================================================
ADIM 8: GÜVENLİK SAĞLAMLAŞTIRMA
================================================================

8.1) Redis güvenlik kontrolleri:
# Redis CLI'da güvenlik komutlarını test et
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis!

# Bu komutlar çalışmamalı (disabled):
FLUSHDB
FLUSHALL
DEBUG

# Çalışırsa redis.conf'da rename-command'ları kontrol et

8.2) Network güvenlik:
# Sadece localhost'tan bağlantıya izin ver
# Firewall kuralını kontrol et
Get-NetFirewallRule -DisplayName "Redis Server"

================================================================
ADIM 9: SORUN GİDERME REHBERİ
================================================================

SORUN 1: Docker kurulumu başarısız
ÇÖZÜM:
# Windows Update'leri kontrol et
Get-WindowsUpdate
# Hyper-V'nin etkin olduğunu kontrol et
Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V

SORUN 2: Redis container başlamıyor
ÇÖZÜM:
# Container loglarını kontrol et
docker logs gymkod-redis-prod
# Port conflict kontrol et
netstat -an | findstr :6379

SORUN 3: .NET uygulaması Redis'e bağlanamıyor
ÇÖZÜM:
# Redis'in çalıştığını kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping
# Connection string'i kontrol et
# Firewall kurallarını kontrol et

SORUN 4: Memory usage yüksek
ÇÖZÜM:
# Cache'leri temizle
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! FLUSHALL
# Memory policy'yi kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! CONFIG GET maxmemory-policy

================================================================
ADIM 10: BAŞARILI KURULUM KONTROLÜ
================================================================

10.1) Tüm servislerin çalıştığını kontrol et:
# Docker service
Get-Service docker

# Redis container
docker ps | findstr gymkod-redis-prod

# Redis health
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

10.2) .NET uygulaması ile test:
# Cache test endpoint'ini çağır
# Performance'ı ölç
# Memory usage'ı kontrol et

================================================================
SONRAKI ADIMLAR
================================================================

✅ KURULUM TAMAMLANDIKTAN SONRA:
1. Load testing (NBomber ile)
2. Monitoring dashboard kurulumu
3. Alert sistemi kurulumu
4. Backup verification
5. Disaster recovery planı

🎯 BÜYÜME PLANI:
- 100 üye → 1000 üye: Memory 4GB → 8GB
- 1000 üye → 10000 üye: Dedicated Redis server
- 10000+ üye: Redis Cluster

================================================================
DESTEK VE YARDIM
================================================================

Bu rehberi takip ederken sorun yaşarsan:
1. Hata mesajını tam olarak kaydet
2. Docker logs'ları kontrol et
3. Windows Event Viewer'ı kontrol et
4. Bana detaylı hata raporu gönder

BAŞARILAR! 🚀
