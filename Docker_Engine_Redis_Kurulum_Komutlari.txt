🎯 DOCKER ENGINE + REDİS KURULUM KOMUTLARI - TEK DOSYA
================================================================
SUNUCU: Windows Server 2019, 4GB RAM, 2 CPU
KLASÖR: C:\GymProject
HEDEF: Production Redis kurulumu Docker Engine ile
================================================================

⚠️ ÖNEMLİ UYARILAR:
- PowerShell'i Administrator olarak aç
- Her komutu tek tek kopyala ve yapıştır
- Sonucunu bekle, hata varsa dur
- Sistem 2 kez yeniden başlatılacak
================================================================

📋 ADIM 1: HAZIRLIK VE KONTROL
================================================================

# PowerShell execution policy ayarla
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

# Mevcut klasörü kontrol et
Test-Path "C:\GymProject"

# Klasöre git (yoksa oluştur)
if (!(Test-Path "C:\GymProject")) { New-Item -ItemType Directory -Path "C:\GymProject" -Force }
cd C:\GymProject

# Sistem bilgilerini kontrol et
Get-ComputerInfo | Select-Object TotalPhysicalMemory, CsProcessors

================================================================
📋 ADIM 2: WINDOWS FEATURES ETKİNLEŞTİRME
================================================================

# Containers feature'ını etkinleştir
Enable-WindowsOptionalFeature -Online -FeatureName containers -All

# Hyper-V'yi etkinleştir
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# ⚠️ UYARI: Sistem yeniden başlatılacak!
Write-Host "Sistem yeniden başlatılacak. Kaydet ve devam et!" -ForegroundColor Red
Restart-Computer

================================================================
📋 ADIM 3: YENİDEN BAŞLADIKTAN SONRA - DOCKER ENGINE KURULUMU
================================================================

# PowerShell'i tekrar Administrator olarak aç ve devam et:
cd C:\GymProject

# Docker Engine kurulum script'ini indir
Invoke-WebRequest -UseBasicParsing "https://raw.githubusercontent.com/microsoft/Windows-Containers/Main/helpful_tools/Install-DockerCE/install-docker-ce.ps1" -o install-docker-ce.ps1

# Docker Engine'i kur
.\install-docker-ce.ps1

# Docker service'ini başlat
Start-Service docker

# Docker'ın çalıştığını kontrol et
docker version

# Docker'ı otomatik başlatılacak şekilde ayarla
Set-Service -Name docker -StartupType Automatic

# Docker info kontrol et
docker info

================================================================
📋 ADIM 4: REDİS KONFİGÜRASYON DOSYALARI OLUŞTURMA
================================================================

# redis.conf dosyası oluştur
@"
# Redis Production Configuration for GymKod
# Windows Server 2019 - 4GB RAM, 2 CPU

# Network
bind 0.0.0.0
port 6379
protected-mode yes
requirepass GymKod2024Redis!

# Memory Management (4GB RAM için optimize)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Security - Tehlikeli komutları devre dışı bırak
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
"@ | Out-File -FilePath "redis.conf" -Encoding UTF8

# docker-compose.prod.yml dosyası oluştur
@"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymKod2024Redis!
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "GymKod2024Redis!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
"@ | Out-File -FilePath "docker-compose.prod.yml" -Encoding UTF8

# Dosyaların oluştuğunu kontrol et
Get-ChildItem -Name "*.conf", "*.yml"

================================================================
📋 ADIM 5: REDİS CONTAINER'I BAŞLATMA
================================================================

# Redis image'ini indir
docker pull redis:7-alpine

# Redis container'ı başlat
docker-compose -f docker-compose.prod.yml up -d

# Container'ın çalıştığını kontrol et
docker ps

# Container loglarını kontrol et
docker logs gymkod-redis-prod

# Redis bağlantısını test et
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

# Redis info kontrol et
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! info server

# Memory bilgilerini kontrol et
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory

================================================================
📋 ADIM 6: WINDOWS FİREWALL AYARLARI
================================================================

# Redis port'unu local network için aç
New-NetFirewallRule -DisplayName "Redis Server Local" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -Profile Domain,Private

# Firewall kuralını kontrol et
Get-NetFirewallRule -DisplayName "Redis Server Local" | Select-Object DisplayName, Enabled, Direction

# Port'un açık olduğunu kontrol et
netstat -an | findstr :6379

================================================================
📋 ADIM 7: MONİTORİNG VE BACKUP KLASÖRLERI
================================================================

# Gerekli klasörleri oluştur
New-Item -ItemType Directory -Path "logs" -Force
New-Item -ItemType Directory -Path "backups" -Force
New-Item -ItemType Directory -Path "backups\redis" -Force
New-Item -ItemType Directory -Path "scripts" -Force

# Klasörlerin oluştuğunu kontrol et
Get-ChildItem -Directory

================================================================
📋 ADIM 8: MONİTORİNG SCRİPTİ OLUŞTURMA
================================================================

# Redis monitoring script'i oluştur
@"
# Redis Monitoring Script for GymKod Production
param(
    [string]`$LogPath = "C:\GymProject\logs\redis-monitor.log"
)

`$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

try {
    # Container'ın çalıştığını kontrol et
    `$containerStatus = docker ps --filter "name=gymkod-redis-prod" --format "{{.Status}}"
    
    if (`$containerStatus -like "*Up*") {
        # Redis'e bağlan ve bilgi al
        `$redisInfo = docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory
        
        if (`$LASTEXITCODE -eq 0) {
            `$memoryUsed = (`$redisInfo | Select-String "used_memory_human:(.+)" | ForEach-Object { `$_.Matches.Groups[1].Value }).Trim()
            `$memoryPeak = (`$redisInfo | Select-String "used_memory_peak_human:(.+)" | ForEach-Object { `$_.Matches.Groups[1].Value }).Trim()
            `$connectedClients = (`$redisInfo | Select-String "connected_clients:(\d+)" | ForEach-Object { `$_.Matches.Groups[1].Value })
            
            `$logEntry = "`$timestamp - Redis OK - Memory: `$memoryUsed, Peak: `$memoryPeak, Clients: `$connectedClients"
            Add-Content -Path `$LogPath -Value `$logEntry
            
            # Memory alert (1.5GB üzeri)
            `$memoryUsedBytes = (`$redisInfo | Select-String "used_memory:(\d+)" | ForEach-Object { `$_.Matches.Groups[1].Value })
            if ([long]`$memoryUsedBytes -gt 1610612736) {
                `$alertEntry = "`$timestamp - ALERT - Redis memory usage high: `$memoryUsed"
                Add-Content -Path `$LogPath -Value `$alertEntry
                Write-Warning "Redis memory usage high: `$memoryUsed"
            }
        } else {
            `$logEntry = "`$timestamp - ERROR - Redis CLI command failed"
            Add-Content -Path `$LogPath -Value `$logEntry
        }
    } else {
        `$logEntry = "`$timestamp - ERROR - Redis container not running"
        Add-Content -Path `$LogPath -Value `$logEntry
    }
} catch {
    `$logEntry = "`$timestamp - ERROR - `$(`$_.Exception.Message)"
    Add-Content -Path `$LogPath -Value `$logEntry
}
"@ | Out-File -FilePath "scripts\redis-monitor.ps1" -Encoding UTF8

================================================================
📋 ADIM 9: BACKUP SCRİPTİ OLUŞTURMA
================================================================

# Redis backup script'i oluştur
@"
# Redis Backup Script for GymKod Production
param(
    [string]`$BackupPath = "C:\GymProject\backups\redis"
)

`$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
`$backupFile = "`$BackupPath\redis-backup-`$timestamp.rdb"

try {
    # Container'ın çalıştığını kontrol et
    `$containerStatus = docker ps --filter "name=gymkod-redis-prod" --format "{{.Status}}"
    
    if (`$containerStatus -like "*Up*") {
        Write-Host "Starting Redis backup..." -ForegroundColor Green
        
        # Redis BGSAVE komutu ile backup al
        `$bgsaveResult = docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! BGSAVE
        
        if (`$LASTEXITCODE -eq 0) {
            Write-Host "BGSAVE command executed, waiting for completion..." -ForegroundColor Yellow
            
            # BGSAVE'in tamamlanmasını bekle
            do {
                Start-Sleep -Seconds 2
                `$lastSave = docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! LASTSAVE
                `$currentTime = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
                `$timeDiff = `$currentTime - [long]`$lastSave
            } while (`$timeDiff -gt 10)  # 10 saniyeden yeni backup bekle
            
            # Backup dosyasını kopyala
            docker cp gymkod-redis-prod:/data/dump.rdb `$backupFile
            
            if (Test-Path `$backupFile) {
                `$fileSize = (Get-Item `$backupFile).Length
                Write-Host "Backup completed successfully: `$backupFile (Size: `$([math]::Round(`$fileSize/1MB, 2)) MB)" -ForegroundColor Green
                
                # Log entry
                `$logEntry = "`$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Backup completed: `$backupFile"
                Add-Content -Path "C:\GymProject\logs\redis-backup.log" -Value `$logEntry
            } else {
                Write-Error "Backup file not created: `$backupFile"
            }
        } else {
            Write-Error "BGSAVE command failed"
        }
    } else {
        Write-Error "Redis container is not running"
    }
} catch {
    Write-Error "Backup failed: `$(`$_.Exception.Message)"
    `$errorEntry = "`$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - Backup failed: `$(`$_.Exception.Message)"
    Add-Content -Path "C:\GymProject\logs\redis-backup.log" -Value `$errorEntry
}

# Eski backup'ları temizle (7 günden eski)
try {
    `$oldBackups = Get-ChildItem `$BackupPath -Filter "redis-backup-*.rdb" | Where-Object { `$_.CreationTime -lt (Get-Date).AddDays(-7) }
    if (`$oldBackups) {
        `$oldBackups | Remove-Item -Force
        Write-Host "Cleaned up `$(`$oldBackups.Count) old backup files" -ForegroundColor Yellow
    }
} catch {
    Write-Warning "Failed to clean up old backups: `$(`$_.Exception.Message)"
}
"@ | Out-File -FilePath "scripts\redis-backup.ps1" -Encoding UTF8

================================================================
📋 ADIM 10: TASK SCHEDULER KURULUMU
================================================================

# Monitoring task'ı oluştur (her 5 dakikada bir)
$monitorAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File C:\GymProject\scripts\redis-monitor.ps1"
$monitorTrigger = New-ScheduledTaskTrigger -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365) -At (Get-Date)
$monitorSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
$monitorPrincipal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount

Register-ScheduledTask -TaskName "GymKod Redis Monitoring" -Action $monitorAction -Trigger $monitorTrigger -Settings $monitorSettings -Principal $monitorPrincipal

# Backup task'ı oluştur (her gün saat 02:00)
$backupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File C:\GymProject\scripts\redis-backup.ps1"
$backupTrigger = New-ScheduledTaskTrigger -Daily -At "02:00"
$backupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
$backupPrincipal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount

Register-ScheduledTask -TaskName "GymKod Redis Backup" -Action $backupAction -Trigger $backupTrigger -Settings $backupSettings -Principal $backupPrincipal

# Task'ların oluştuğunu kontrol et
Get-ScheduledTask | Where-Object {$_.TaskName -like "*GymKod*"} | Select-Object TaskName, State

================================================================
📋 ADIM 11: SCRİPTLERİ MANUEL TEST ETME
================================================================

# Monitoring script'ini test et
.\scripts\redis-monitor.ps1

# Log dosyasını kontrol et
Get-Content "logs\redis-monitor.log" -Tail 3

# Backup script'ini test et
.\scripts\redis-backup.ps1

# Backup dosyasının oluştuğunu kontrol et
Get-ChildItem "backups\redis" | Sort-Object CreationTime -Descending | Select-Object -First 3

================================================================
📋 ADIM 12: PERFORMANCE OPTİMİZASYONU
================================================================

# Docker container resource kullanımını kontrol et
docker stats gymkod-redis-prod --no-stream

# Redis performance bilgilerini al
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info stats

# Redis slow log'u kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! slowlog get 5

# Redis configuration'ı kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! config get "*memory*"

================================================================
📋 ADIM 13: KURULUM KONTROLÜ VE DOĞRULAMA
================================================================

# Tüm servislerin durumunu kontrol et
Write-Host "=== DOCKER SERVICE ===" -ForegroundColor Green
Get-Service docker | Select-Object Name, Status, StartType

Write-Host "=== REDIS CONTAINER ===" -ForegroundColor Green
docker ps --filter "name=gymkod-redis-prod" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

Write-Host "=== REDIS CONNECTION TEST ===" -ForegroundColor Green
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

Write-Host "=== REDIS MEMORY INFO ===" -ForegroundColor Green
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory | findstr "used_memory_human\|maxmemory_human"

Write-Host "=== FIREWALL RULES ===" -ForegroundColor Green
Get-NetFirewallRule -DisplayName "Redis Server Local" | Select-Object DisplayName, Enabled, Direction

Write-Host "=== SCHEDULED TASKS ===" -ForegroundColor Green
Get-ScheduledTask | Where-Object {$_.TaskName -like "*GymKod*"} | Select-Object TaskName, State

Write-Host "=== DOSYA YAPISI ===" -ForegroundColor Green
Get-ChildItem -Recurse | Where-Object {!$_.PSIsContainer} | Select-Object FullName

Write-Host "=== PORT KONTROLÜ ===" -ForegroundColor Green
netstat -an | findstr :6379

================================================================
📋 ADIM 14: .NET UYGULAMASINI TEST ETME
================================================================

# .NET uygulamasının klasörüne git
cd C:\GymProject\GymProjectBackend\WebAPI

# appsettings.json'da Redis connection string'ini kontrol et
Get-Content "appsettings.json" | Select-String "Redis" -Context 2

# Uygulamayı production modunda çalıştır
Write-Host "Starting .NET application in production mode..." -ForegroundColor Yellow
dotnet run --environment canlı

# ⚠️ Başka bir PowerShell penceresi açıp test yapabilirsin:
# Invoke-RestMethod -Uri "http://localhost:5165/swagger" -Method GET

================================================================
📋 ADIM 15: SORUN GİDERME KOMUTLARI
================================================================

# Docker service sorunları
if ((Get-Service docker).Status -ne "Running") {
    Write-Host "Docker service başlatılıyor..." -ForegroundColor Yellow
    Start-Service docker
}

# Container sorunları
if (!(docker ps --filter "name=gymkod-redis-prod" --format "{{.Names}}")) {
    Write-Host "Redis container başlatılıyor..." -ForegroundColor Yellow
    docker-compose -f docker-compose.prod.yml up -d
}

# Redis bağlantı sorunları
$redisTest = docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping 2>$null
if ($redisTest -ne "PONG") {
    Write-Host "Redis bağlantı sorunu tespit edildi!" -ForegroundColor Red
    docker logs gymkod-redis-prod --tail 20
}

# Container'ı yeniden başlat (gerekirse)
# docker restart gymkod-redis-prod

# Container loglarını detaylı kontrol et
# docker logs gymkod-redis-prod --tail 50

# Redis CLI'ye manuel bağlan
# docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis!

================================================================
📋 BAŞARILI KURULUM ONAY LİSTESİ
================================================================

Write-Host "=== KURULUM DOĞRULAMA ===" -ForegroundColor Cyan

$checks = @()

# Docker service kontrolü
$dockerService = Get-Service docker
$checks += [PSCustomObject]@{
    Check = "Docker Service"
    Status = if ($dockerService.Status -eq "Running") { "✅ OK" } else { "❌ FAIL" }
    Detail = $dockerService.Status
}

# Redis container kontrolü
$redisContainer = docker ps --filter "name=gymkod-redis-prod" --format "{{.Status}}"
$checks += [PSCustomObject]@{
    Check = "Redis Container"
    Status = if ($redisContainer -like "*Up*") { "✅ OK" } else { "❌ FAIL" }
    Detail = $redisContainer
}

# Redis bağlantı kontrolü
$redisPing = docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping 2>$null
$checks += [PSCustomObject]@{
    Check = "Redis Connection"
    Status = if ($redisPing -eq "PONG") { "✅ OK" } else { "❌ FAIL" }
    Detail = $redisPing
}

# Firewall kontrolü
$firewallRule = Get-NetFirewallRule -DisplayName "Redis Server Local" -ErrorAction SilentlyContinue
$checks += [PSCustomObject]@{
    Check = "Firewall Rule"
    Status = if ($firewallRule -and $firewallRule.Enabled -eq "True") { "✅ OK" } else { "❌ FAIL" }
    Detail = if ($firewallRule) { $firewallRule.Enabled } else { "Not Found" }
}

# Scheduled tasks kontrolü
$tasks = Get-ScheduledTask | Where-Object {$_.TaskName -like "*GymKod*"}
$checks += [PSCustomObject]@{
    Check = "Scheduled Tasks"
    Status = if ($tasks.Count -eq 2) { "✅ OK" } else { "❌ FAIL" }
    Detail = "$($tasks.Count) tasks found"
}

# Port kontrolü
$portCheck = netstat -an | findstr :6379
$checks += [PSCustomObject]@{
    Check = "Port 6379"
    Status = if ($portCheck) { "✅ OK" } else { "❌ FAIL" }
    Detail = if ($portCheck) { "Listening" } else { "Not Listening" }
}

$checks | Format-Table -AutoSize

$successCount = ($checks | Where-Object {$_.Status -eq "✅ OK"}).Count
$totalCount = $checks.Count

Write-Host "`n=== KURULUM SONUCU ===" -ForegroundColor Cyan
if ($successCount -eq $totalCount) {
    Write-Host "🎉 KURULUM BAŞARILI! ($successCount/$totalCount)" -ForegroundColor Green
    Write-Host "Redis production ortamında çalışmaya hazır!" -ForegroundColor Green
} else {
    Write-Host "⚠️ KURULUM KISMEN BAŞARILI ($successCount/$totalCount)" -ForegroundColor Yellow
    Write-Host "Başarısız kontrolleri gözden geçirin." -ForegroundColor Yellow
}

Write-Host "`n=== SONRAKI ADIMLAR ===" -ForegroundColor Cyan
Write-Host "1. .NET uygulamasını test edin" -ForegroundColor White
Write-Host "2. Cache performance'ını ölçün" -ForegroundColor White
Write-Host "3. Monitoring loglarını kontrol edin" -ForegroundColor White
Write-Host "4. Backup'ları doğrulayın" -ForegroundColor White

Write-Host "`nKURULUM TAMAMLANDI! 🚀" -ForegroundColor Green

================================================================
NOTLAR VE İPUÇLARI
================================================================

✅ BAŞARILI KURULUM İÇİN:
- Her komutu sırayla çalıştır
- Hata mesajlarını oku ve kaydet
- Yeniden başlatma sonrası devam et
- Test komutlarını mutlaka çalıştır

❌ SORUN YAŞARSAN:
- docker logs gymkod-redis-prod
- Get-EventLog -LogName Application -Source Docker
- Windows Event Viewer kontrol et
- Hata mesajını tam olarak kaydet

🎯 PERFORMANS İZLEME:
- logs\redis-monitor.log dosyasını takip et
- docker stats gymkod-redis-prod
- Task Manager'da memory kullanımını izle

🔧 BAKIM:
- Günlük backup'lar otomatik alınacak
- 7 günden eski backup'lar otomatik silinecek
- Memory kullanımı 1.5GB üzeri uyarı verecek

BAŞARILAR! 🚀

================================================================
EK YARDIMCI KOMUTLAR VE İPUÇLARI
================================================================

📋 REDIS YÖNETİM KOMUTLARI
================================================================

# Redis CLI'ye bağlan
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis!

# Redis içinde kullanabileceğin komutlar:
# ping                          # Bağlantı testi
# info memory                   # Memory bilgileri
# info stats                    # İstatistikler
# keys gym:*                    # GymKod cache key'lerini listele
# get gym:1:member:details      # Belirli bir cache'i görüntüle
# ttl gym:1:member:details      # Cache'in kalan süresini gör
# dbsize                        # Toplam key sayısı
# flushall                      # TÜM CACHE'İ TEMİZLE (DİKKAT!)
# exit                          # CLI'den çık

# Container dışından Redis komutları
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! keys "gym:*"

================================================================
📋 DOCKER YÖNETİM KOMUTLARI
================================================================

# Container durumunu kontrol et
docker ps -a

# Container'ı durdur
docker stop gymkod-redis-prod

# Container'ı başlat
docker start gymkod-redis-prod

# Container'ı yeniden başlat
docker restart gymkod-redis-prod

# Container loglarını canlı takip et
docker logs -f gymkod-redis-prod

# Container resource kullanımını canlı izle
docker stats gymkod-redis-prod

# Container'a shell ile bağlan
docker exec -it gymkod-redis-prod sh

# Container'ı tamamen sil (DİKKAT! Veri kaybı)
# docker rm -f gymkod-redis-prod

# Volume'ları listele
docker volume ls

# Redis volume'unu kontrol et
docker volume inspect GymProject_redis_data

================================================================
📋 PERFORMANS İZLEME VE OPTİMİZASYON
================================================================

# Sistem kaynak kullanımını kontrol et
Get-Counter "\Memory\Available MBytes"
Get-Counter "\Processor(_Total)\% Processor Time"

# Redis memory kullanımını detaylı kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory | findstr "used_memory\|maxmemory\|mem_fragmentation"

# Redis slow query'leri kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! slowlog get 10

# Redis client bağlantılarını kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! client list

# Cache hit/miss oranını kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info stats | findstr "keyspace_hits\|keyspace_misses"

================================================================
📋 BACKUP VE RESTORE İŞLEMLERİ
================================================================

# Manuel backup al
.\scripts\redis-backup.ps1

# Backup dosyalarını listele
Get-ChildItem "backups\redis" | Sort-Object CreationTime -Descending

# Backup'tan restore et (DİKKAT! Mevcut veri silinir)
# 1. Container'ı durdur
# docker stop gymkod-redis-prod
# 2. Backup dosyasını kopyala
# docker cp "backups\redis\redis-backup-YYYYMMDD-HHMMSS.rdb" gymkod-redis-prod:/data/dump.rdb
# 3. Container'ı başlat
# docker start gymkod-redis-prod

# Otomatik backup'ları kontrol et
Get-ScheduledTask -TaskName "GymKod Redis Backup" | Get-ScheduledTaskInfo

================================================================
📋 GÜVENLİK VE BAKIM
================================================================

# Redis konfigürasyonunu kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! config get "*"

# Güvenlik ayarlarını kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! config get "requirepass"
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! config get "bind"

# Log dosyalarını kontrol et
Get-Content "logs\redis-monitor.log" -Tail 20
Get-Content "logs\redis-backup.log" -Tail 10

# Disk kullanımını kontrol et
Get-ChildItem "backups\redis" | Measure-Object -Property Length -Sum | Select-Object @{Name="TotalSizeMB";Expression={[math]::Round($_.Sum/1MB,2)}}

# Eski log dosyalarını temizle (30 günden eski)
Get-ChildItem "logs" -Filter "*.log" | Where-Object {$_.CreationTime -lt (Get-Date).AddDays(-30)} | Remove-Item

================================================================
📋 SORUN GİDERME REHBERİ
================================================================

SORUN 1: Docker service başlamıyor
ÇÖZÜM:
Get-Service docker
Start-Service docker
Get-EventLog -LogName Application -Source Docker -Newest 5

SORUN 2: Redis container başlamıyor
ÇÖZÜM:
docker logs gymkod-redis-prod
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d

SORUN 3: Redis'e bağlanamıyor
ÇÖZÜM:
netstat -an | findstr :6379
Get-NetFirewallRule -DisplayName "Redis Server Local"
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

SORUN 4: Memory kullanımı yüksek
ÇÖZÜM:
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! memory usage gym:*
# Gerekirse cache'leri temizle:
# docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! flushall

SORUN 5: Performance düşük
ÇÖZÜM:
docker stats gymkod-redis-prod
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! slowlog get 10
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info stats

SORUN 6: Backup başarısız
ÇÖZÜM:
Test-Path "backups\redis"
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! bgsave
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! lastsave

================================================================
📋 .NET UYGULAMASI ENTEGRASYONU
================================================================

# .NET uygulamasının Redis'e bağlanıp bağlanmadığını test et
cd C:\GymProject\GymProjectBackend\WebAPI

# appsettings.json'da Redis ayarlarını kontrol et
Get-Content "appsettings.json" | Select-String "Redis" -Context 3

# Uygulamayı test modunda çalıştır
dotnet run --environment canlı

# Cache test endpoint'leri (başka PowerShell'de):
# Invoke-RestMethod -Uri "http://localhost:5165/api/member/getmemberdetails" -Method GET
# Invoke-RestMethod -Uri "http://localhost:5165/api/member/getallpaginated?page=1&size=10" -Method GET

# Cache key'lerinin oluştuğunu kontrol et
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! keys "gym:*"

# Cache performance'ını ölç
Measure-Command { Invoke-RestMethod -Uri "http://localhost:5165/api/member/getmemberdetails" -Method GET }

================================================================
📋 BÜYÜME VE SCALİNG PLANI
================================================================

# Mevcut durumunuz: 1 salon, 100 üye
# Redis Memory: 2GB limit
# Expected cache size: ~50-100MB

# 10 salon, 1000 üye için:
# Redis Memory: 4GB'a çıkarın
# docker-compose.prod.yml'de memory limit'i güncelleyin

# 100 salon, 10000 üye için:
# Dedicated Redis server düşünün
# Redis Cluster kurulumu gerekebilir
# Monitoring'i artırın

# Memory limit'i güncelleme:
# docker-compose.prod.yml'de:
# memory: 4G (2G yerine)
# maxmemory 4gb (redis.conf'da)

================================================================
📋 HIZLI REFERANS KOMUTLARI
================================================================

# Sistem durumu özeti
Write-Host "=== HIZLI DURUM KONTROLÜ ===" -ForegroundColor Cyan
Write-Host "Docker Service: $((Get-Service docker).Status)" -ForegroundColor White
Write-Host "Redis Container: $(docker ps --filter 'name=gymkod-redis-prod' --format '{{.Status}}')" -ForegroundColor White
Write-Host "Redis Connection: $(docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping 2>$null)" -ForegroundColor White
Write-Host "Redis Memory: $(docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory 2>$null | Select-String 'used_memory_human' | ForEach-Object {$_.ToString().Split(':')[1].Trim()})" -ForegroundColor White

# Acil durum komutları
# docker restart gymkod-redis-prod                    # Redis'i yeniden başlat
# docker-compose -f docker-compose.prod.yml restart   # Tüm servisleri yeniden başlat
# .\scripts\redis-backup.ps1                          # Acil backup al
# docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! flushall  # TÜM CACHE'İ TEMİZLE

================================================================
📋 DESTEK VE YARDIM
================================================================

Bu kurulum rehberini takip ederken sorun yaşarsan:

1. HATA MESAJINI KAYDET:
   - Tam hata metnini kopyala
   - Hangi adımda hata aldığını belirt
   - Ekran görüntüsü al

2. LOG DOSYALARINI KONTROL ET:
   - docker logs gymkod-redis-prod
   - Get-Content "logs\redis-monitor.log" -Tail 20
   - Windows Event Viewer

3. SİSTEM BİLGİLERİNİ TOPLA:
   - Get-ComputerInfo | Select-Object TotalPhysicalMemory, CsProcessors
   - docker version
   - docker info

4. DETAYLI RAPOR HAZIRLA:
   - Hangi adımda takıldığın
   - Hata mesajları
   - Sistem bilgileri
   - Denediğin çözümler

Bu bilgilerle birlikte yardım isteyebilirsin!

================================================================
BAŞARILAR VE İYİ ÇALIŞMALAR! 🚀
================================================================

Bu rehber ile Windows Server 2019'da production-ready Redis kurulumu
tamamlamış olacaksın. Sistem 1000+ salon ve 100.000+ kullanıcıya kadar
ölçeklenebilir şekilde tasarlandı.

Kurulum sonrası mutlaka:
✅ Performance testleri yap
✅ Backup'ları doğrula
✅ Monitoring'i kontrol et
✅ .NET uygulamasını test et

BAŞARILAR! 🎯
