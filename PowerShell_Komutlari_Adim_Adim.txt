🎯 POWERSHELL KOMUTLARI - ADIM ADIM KOPYALA YAPIŞTIR
================================================================
UYARI: Her komutu tek tek kopyala, yapıştır ve sonucunu bekle!
KLASÖR: C:\gymproject (mevcut klasörün içine kurulacak)
================================================================

📋 ADIM 1: HAZIRLIK VE KONTROL
================================================================

# PowerShell'i Administrator olarak aç ve şu komutu çalıştır:
Get-ExecutionPolicy

# Eğer "Restricted" dönerse şunu çalıştır:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

# Mevcut klasörü kontrol et:
Test-Path "C:\gymproject"

# Eğer false dönerse klasörü oluştur:
New-Item -ItemType Directory -Path "C:\gymproject" -Force

# Klasöre git:
cd C:\gymproject

================================================================
📋 ADIM 2: DOCKER ENGINE KURULUMU (İLK SEÇENEK)
================================================================

# Windows Container feature'ını etkinleştir:
Enable-WindowsOptionalFeature -Online -FeatureName containers -All

# Hyper-V'yi etkinleştir:
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# ⚠️ UYARI: Sistem yeniden başlatılacak! Kaydet ve devam et:
Restart-Computer

# ===== YENİDEN BAŞLADIKTAN SONRA =====
# PowerShell'i tekrar Administrator olarak aç ve devam et:

cd C:\gymproject

# Docker Engine kurulum script'ini indir:
Invoke-WebRequest -UseBasicParsing "https://raw.githubusercontent.com/microsoft/Windows-Containers/Main/helpful_tools/Install-DockerCE/install-docker-ce.ps1" -o install-docker-ce.ps1

# Docker Engine'i kur:
.\install-docker-ce.ps1

# Docker service'ini başlat:
Start-Service docker

# Docker'ın çalıştığını kontrol et:
docker version

# Docker'ı otomatik başlatılacak şekilde ayarla:
Set-Service -Name docker -StartupType Automatic

================================================================
📋 ADIM 3: REDİS KONFİGÜRASYON DOSYALARI OLUŞTURMA
================================================================

# Redis konfigürasyon dosyası oluştur:
New-Item -ItemType File -Path "redis.conf" -Force

# redis.conf dosyasının içeriğini yaz:
@"
# Redis Production Configuration for GymKod
# Windows Server 2019 - 4GB RAM, 2 CPU

# Network
bind 0.0.0.0
port 6379
protected-mode yes
requirepass GymKod2024Redis!

# Memory Management (4GB RAM için optimize)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
"@ | Out-File -FilePath "redis.conf" -Encoding UTF8

# docker-compose.prod.yml dosyası oluştur:
@"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymKod2024Redis!
    networks:
      - gymkod-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "GymKod2024Redis!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
"@ | Out-File -FilePath "docker-compose.prod.yml" -Encoding UTF8

================================================================
📋 ADIM 4: REDİS CONTAINER'I BAŞLATMA
================================================================

# Redis container'ı başlat:
docker-compose -f docker-compose.prod.yml up -d

# Container'ın çalıştığını kontrol et:
docker ps

# Container loglarını kontrol et:
docker logs gymkod-redis-prod

# Redis bağlantısını test et:
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

# Memory bilgilerini kontrol et:
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory

================================================================
📋 ADIM 5: WINDOWS FİREWALL AYARLARI
================================================================

# Redis port'unu local network için aç:
New-NetFirewallRule -DisplayName "Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -Profile Domain,Private

# Firewall kuralını kontrol et:
Get-NetFirewallRule -DisplayName "Redis Server"

================================================================
📋 ADIM 6: MONİTORİNG VE BACKUP SCRİPTLERİ
================================================================

# Log klasörünü oluştur:
New-Item -ItemType Directory -Path "logs" -Force
New-Item -ItemType Directory -Path "backups\redis" -Force

# Redis monitoring script'i oluştur:
@"
# Redis Monitoring Script for GymKod
param(
    [string]`$LogPath = "C:\gymproject\logs\redis-monitor.log"
)

`$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

try {
    `$redisInfo = docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory
    
    if (`$LASTEXITCODE -eq 0) {
        `$memoryUsed = (`$redisInfo | Select-String "used_memory_human:(.+)" | ForEach-Object { `$_.Matches.Groups[1].Value }).Trim()
        `$memoryPeak = (`$redisInfo | Select-String "used_memory_peak_human:(.+)" | ForEach-Object { `$_.Matches.Groups[1].Value }).Trim()
        
        `$logEntry = "`$timestamp - Redis OK - Memory Used: `$memoryUsed, Peak: `$memoryPeak"
        Add-Content -Path `$LogPath -Value `$logEntry
        
        # Memory alert (1.5GB üzeri)
        `$memoryUsedBytes = (`$redisInfo | Select-String "used_memory:(\d+)" | ForEach-Object { `$_.Matches.Groups[1].Value })
        if ([long]`$memoryUsedBytes -gt 1610612736) {
            Write-Warning "Redis memory usage high: `$memoryUsed"
        }
    } else {
        `$logEntry = "`$timestamp - Redis ERROR - Connection failed"
        Add-Content -Path `$LogPath -Value `$logEntry
    }
} catch {
    `$logEntry = "`$timestamp - Redis ERROR - `$(`$_.Exception.Message)"
    Add-Content -Path `$LogPath -Value `$logEntry
}
"@ | Out-File -FilePath "redis-monitor.ps1" -Encoding UTF8

# Redis backup script'i oluştur:
@"
# Redis Backup Script for GymKod
param(
    [string]`$BackupPath = "C:\gymproject\backups\redis"
)

`$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
`$backupFile = "`$BackupPath\redis-backup-`$timestamp.rdb"

# Backup klasörünü oluştur
if (!(Test-Path `$BackupPath)) {
    New-Item -ItemType Directory -Path `$BackupPath -Force
}

try {
    # Redis BGSAVE komutu ile backup al
    docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! BGSAVE
    
    if (`$LASTEXITCODE -eq 0) {
        # Backup dosyasını kopyala
        Start-Sleep -Seconds 10  # BGSAVE'in tamamlanmasını bekle
        docker cp gymkod-redis-prod:/data/dump.rdb `$backupFile
        Write-Host "Backup completed: `$backupFile"
    } else {
        Write-Error "BGSAVE command failed"
    }
} catch {
    Write-Error "Backup failed: `$(`$_.Exception.Message)"
}

# Eski backup'ları temizle (7 günden eski)
Get-ChildItem `$BackupPath -Filter "redis-backup-*.rdb" | Where-Object { `$_.CreationTime -lt (Get-Date).AddDays(-7) } | Remove-Item
"@ | Out-File -FilePath "redis-backup.ps1" -Encoding UTF8

================================================================
📋 ADIM 7: TASK SCHEDULER KURULUMU
================================================================

# Monitoring task'ı oluştur (her 5 dakikada bir):
$action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File C:\gymproject\redis-monitor.ps1"
$trigger = New-ScheduledTaskTrigger -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365) -At (Get-Date)
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount

Register-ScheduledTask -TaskName "Redis Monitoring" -Action $action -Trigger $trigger -Settings $settings -Principal $principal

# Backup task'ı oluştur (her gün saat 02:00):
$backupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File C:\gymproject\redis-backup.ps1"
$backupTrigger = New-ScheduledTaskTrigger -Daily -At "02:00"
$backupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
$backupPrincipal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount

Register-ScheduledTask -TaskName "Redis Backup" -Action $backupAction -Trigger $backupTrigger -Settings $backupSettings -Principal $backupPrincipal

================================================================
📋 ADIM 8: KURULUM KONTROLÜ VE TEST
================================================================

# Docker service durumunu kontrol et:
Get-Service docker

# Redis container durumunu kontrol et:
docker ps | findstr gymkod-redis-prod

# Redis health check:
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

# Redis memory bilgilerini kontrol et:
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory

# Scheduled task'ları kontrol et:
Get-ScheduledTask | Where-Object {$_.TaskName -like "*Redis*"}

# Monitoring script'ini manuel test et:
.\redis-monitor.ps1

# Backup script'ini manuel test et:
.\redis-backup.ps1

# Log dosyasını kontrol et:
Get-Content "logs\redis-monitor.log" -Tail 5

================================================================
📋 ADIM 9: .NET UYGULAMASINI TEST ETME
================================================================

# .NET uygulamasının klasörüne git:
cd C:\gymproject\GymProjectBackend\WebAPI

# Uygulamayı production modunda çalıştır:
dotnet run --environment canlı

# ⚠️ Başka bir PowerShell penceresi aç ve test et:
# Cache test (eğer endpoint varsa):
Invoke-RestMethod -Uri "http://localhost:5165/api/member/getmemberdetails" -Method GET

================================================================
📋 ADIM 10: SORUN GİDERME KOMUTLARI
================================================================

# Docker loglarını kontrol et:
docker logs gymkod-redis-prod

# Container'ı yeniden başlat:
docker restart gymkod-redis-prod

# Redis CLI'ye bağlan:
docker exec -it gymkod-redis-prod redis-cli -a GymKod2024Redis!

# Redis içinde test komutları:
# ping
# info memory
# keys gym:*
# exit

# Port'un açık olduğunu kontrol et:
netstat -an | findstr :6379

# Firewall kurallarını kontrol et:
Get-NetFirewallRule -DisplayName "Redis Server"

================================================================
📋 BAŞARILI KURULUM KONTROLÜ
================================================================

# Tüm servislerin durumunu kontrol et:
Write-Host "=== DOCKER SERVICE ===" -ForegroundColor Green
Get-Service docker

Write-Host "=== REDIS CONTAINER ===" -ForegroundColor Green
docker ps | findstr gymkod-redis-prod

Write-Host "=== REDIS CONNECTION ===" -ForegroundColor Green
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! ping

Write-Host "=== REDIS MEMORY ===" -ForegroundColor Green
docker exec gymkod-redis-prod redis-cli -a GymKod2024Redis! info memory | findstr used_memory_human

Write-Host "=== SCHEDULED TASKS ===" -ForegroundColor Green
Get-ScheduledTask | Where-Object {$_.TaskName -like "*Redis*"} | Select-Object TaskName, State

Write-Host "=== FIREWALL RULES ===" -ForegroundColor Green
Get-NetFirewallRule -DisplayName "Redis Server" | Select-Object DisplayName, Enabled, Direction

Write-Host "=== KURULUM TAMAMLANDI! ===" -ForegroundColor Yellow

================================================================
📋 DOCKER KURULUMU BAŞARISIZ OLURSA - ALTERNATİF
================================================================

# Eğer Docker kurulumu başarısız olursa Redis Windows Native kurulumu:

# Redis Windows MSI'yi indir:
$redisUrl = "https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.msi"
$downloadPath = "C:\gymproject\Redis-x64-3.0.504.msi"
Invoke-WebRequest -Uri $redisUrl -OutFile $downloadPath

# MSI'yi çalıştır:
Start-Process -FilePath $downloadPath -Wait

# Redis service'ini başlat:
Start-Service Redis

# Redis'i test et:
redis-cli -a GymKod2024Redis! ping

================================================================
NOTLAR VE İPUÇLARI
================================================================

✅ BAŞARILI KURULUM İÇİN:
1. Her komutu tek tek çalıştır
2. Hata mesajlarını oku ve kaydet
3. Yeniden başlatma gerektiğinde kaydet
4. Test komutlarını mutlaka çalıştır

❌ SORUN YAŞARSAN:
1. Hata mesajını tam olarak kaydet
2. Docker logs'ları kontrol et
3. Windows Event Viewer'ı kontrol et
4. Bana detaylı rapor gönder

🎯 SONRAKI ADIMLAR:
1. .NET uygulamasını test et
2. Cache performance'ını ölç
3. Monitoring'i kontrol et
4. Backup'ları doğrula

BAŞARILAR! 🚀
