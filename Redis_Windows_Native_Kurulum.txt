🎯 ALTERNATİF: REDİS WINDOWS NATIVE KURULUM REHBERİ
================================================================
DURUM: Docker Engine kurulumu başarısız olursa bu yöntemi kullan
AVANTAJ: <PERSON>ha <PERSON> kayna<PERSON> kullanır, Windows Service olarak çalışır
DEZAVANTAJ: Konfigürasyon daha karmaşık, Microsoft port'u (eski)
================================================================

📋 ADIM 1: REDİS WINDOWS SÜRÜMÜNÜ İNDİRME
================================================================

1.1) PowerShell'i Administrator olarak aç
1.2) Redis Windows sürümünü indir:

# Microsoft'un Redis port'unu indir (en son stable)
$redisUrl = "https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.msi"
$downloadPath = "C:\gymproject\Redis-x64-3.0.504.msi"

# İndirme klasörünü oluştur
if (!(Test-Path "C:\gymproject")) {
    New-Item -ItemType Directory -Path "C:\gymproject" -Force
}

# Redis MSI'yi indir
Invoke-WebRequest -Uri $redisUrl -OutFile $downloadPath
Write-Host "Redis MSI indirildi: $downloadPath"

================================================================
ADIM 2: REDİS KURULUMU
================================================================

2.1) MSI installer'ı çalıştır:
Start-Process -FilePath $downloadPath -Wait

# Kurulum sırasında:
# - Installation path: C:\Program Files\Redis
# - Add Redis to PATH: ✅ İşaretle
# - Install Redis as Windows Service: ✅ İşaretle
# - Service name: Redis
# - Port: 6379 (default)

2.2) Kurulumu doğrula:
# Redis service'inin çalıştığını kontrol et
Get-Service Redis

# Redis CLI'yi test et
redis-cli ping
# PONG döner

================================================================
ADIM 3: REDİS KONFİGÜRASYONU
================================================================

3.1) Redis konfigürasyon dosyasını düzenle:
$redisConfigPath = "C:\Program Files\Redis\redis.windows-service.conf"
notepad $redisConfigPath

3.2) Aşağıdaki ayarları ekle/değiştir:

# Security
requirepass GymKod2024Redis!
bind 127.0.0.1

# Memory Management (4GB RAM için)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# Performance
tcp-keepalive 300
timeout 0

# Logging
loglevel notice
logfile C:\gymproject\logs\redis.log

# Security - Tehlikeli komutları devre dışı bırak
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG ""

3.3) Log klasörünü oluştur:
New-Item -ItemType Directory -Path "C:\gymproject\logs" -Force

================================================================
ADIM 4: REDİS SERVİSİNİ YENİDEN BAŞLATMA
================================================================

4.1) Redis service'ini durdur ve başlat:
Stop-Service Redis
Start-Service Redis

4.2) Service'in çalıştığını kontrol et:
Get-Service Redis
# Status: Running olmalı

4.3) Yeni konfigürasyonu test et:
redis-cli -a GymKod2024Redis!
ping
# PONG döner

info memory
# Memory ayarlarını kontrol et

exit

================================================================
ADIM 5: WINDOWS FİREWALL AYARLARI
================================================================

5.1) Redis port'unu local network için aç:
New-NetFirewallRule -DisplayName "Redis Server Local" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -Profile Private,Domain

# Public profile'a ekleme!

5.2) Firewall kuralını kontrol et:
Get-NetFirewallRule -DisplayName "Redis Server Local"

================================================================
ADIM 6: .NET UYGULAMASINI BAĞLAMA
================================================================

6.1) appsettings.json'da connection string'i güncelle:
# Mevcut:
"canlı": "localhost:6379,password=GymKod2024Redis!,abortConnect=false"

# Yeni (aynı kalacak):
"canlı": "localhost:6379,password=GymKod2024Redis!,abortConnect=false,connectRetry=3,connectTimeout=5000"

6.2) .NET uygulamasını test et:
cd C:\gymproject\GymProjectBackend\WebAPI
dotnet run --environment canlı

================================================================
ADIM 7: MONİTORİNG VE BACKUP
================================================================

7.1) Redis monitoring script'i (Windows Native için):
New-Item -ItemType File -Path "C:\gymproject\redis-monitor-native.ps1" -Force
notepad "C:\gymproject\redis-monitor-native.ps1"

# İçerik:
param(
    [string]$LogPath = "C:\gymproject\logs\redis-monitor.log"
)

$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

try {
    # Redis'e bağlan ve bilgi al
    $redisInfo = redis-cli -a GymKod2024Redis! info memory
    
    if ($LASTEXITCODE -eq 0) {
        $memoryUsed = ($redisInfo | Select-String "used_memory_human:(.+)" | ForEach-Object { $_.Matches.Groups[1].Value }).Trim()
        $memoryPeak = ($redisInfo | Select-String "used_memory_peak_human:(.+)" | ForEach-Object { $_.Matches.Groups[1].Value }).Trim()
        
        $logEntry = "$timestamp - Redis OK - Memory Used: $memoryUsed, Peak: $memoryPeak"
        Add-Content -Path $LogPath -Value $logEntry
        
        # Memory alert (1.5GB üzeri)
        $memoryUsedBytes = ($redisInfo | Select-String "used_memory:(\d+)" | ForEach-Object { $_.Matches.Groups[1].Value })
        if ([long]$memoryUsedBytes -gt 1610612736) {
            Write-Warning "Redis memory usage high: $memoryUsed"
        }
    } else {
        $logEntry = "$timestamp - Redis ERROR - Connection failed"
        Add-Content -Path $LogPath -Value $logEntry
    }
} catch {
    $logEntry = "$timestamp - Redis ERROR - $($_.Exception.Message)"
    Add-Content -Path $LogPath -Value $logEntry
}

7.2) Backup script'i (Windows Native için):
New-Item -ItemType File -Path "C:\gymproject\redis-backup-native.ps1" -Force
notepad "C:\gymproject\redis-backup-native.ps1"

# İçerik:
param(
    [string]$BackupPath = "C:\gymproject\backups\redis"
)

$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupFile = "$BackupPath\redis-backup-$timestamp.rdb"

# Backup klasörünü oluştur
if (!(Test-Path $BackupPath)) {
    New-Item -ItemType Directory -Path $BackupPath -Force
}

try {
    # Redis BGSAVE komutu
    redis-cli -a GymKod2024Redis! BGSAVE
    
    if ($LASTEXITCODE -eq 0) {
        # BGSAVE'in tamamlanmasını bekle
        Start-Sleep -Seconds 10
        
        # dump.rdb dosyasını kopyala
        $rdbSource = "C:\Program Files\Redis\dump.rdb"
        if (Test-Path $rdbSource) {
            Copy-Item $rdbSource $backupFile
            Write-Host "Backup completed: $backupFile"
        } else {
            Write-Error "dump.rdb file not found at $rdbSource"
        }
    } else {
        Write-Error "BGSAVE command failed"
    }
} catch {
    Write-Error "Backup failed: $($_.Exception.Message)"
}

# Eski backup'ları temizle (7 günden eski)
Get-ChildItem $BackupPath -Filter "redis-backup-*.rdb" | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-7) } | Remove-Item

================================================================
ADIM 8: TASK SCHEDULER KURULUMU
================================================================

8.1) Monitoring task'ı oluştur:
$action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File C:\gymproject\redis-monitor-native.ps1"
$trigger = New-ScheduledTaskTrigger -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365) -At (Get-Date)
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount

Register-ScheduledTask -TaskName "Redis Monitoring" -Action $action -Trigger $trigger -Settings $settings -Principal $principal

8.2) Backup task'ı oluştur:
$backupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-File C:\gymproject\redis-backup-native.ps1"
$backupTrigger = New-ScheduledTaskTrigger -Daily -At "02:00"
$backupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
$backupPrincipal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount

Register-ScheduledTask -TaskName "Redis Backup" -Action $backupAction -Trigger $backupTrigger -Settings $backupSettings -Principal $backupPrincipal

================================================================
ADIM 9: PERFORMANCE OPTİMİZASYONU
================================================================

9.1) Windows Service ayarları:
# Redis service'inin otomatik başlamasını sağla
Set-Service -Name Redis -StartupType Automatic

# Service recovery ayarları
sc.exe failure Redis reset= 86400 actions= restart/5000/restart/5000/restart/5000

9.2) Memory ayarları kontrol:
redis-cli -a GymKod2024Redis! CONFIG GET maxmemory
redis-cli -a GymKod2024Redis! CONFIG GET maxmemory-policy

================================================================
ADIM 10: SORUN GİDERME
================================================================

SORUN 1: Redis service başlamıyor
ÇÖZÜM:
# Event Viewer'ı kontrol et
eventvwr.msc
# Windows Logs > Application > Redis hatalarını ara

# Service'i manuel başlat
net start Redis

SORUN 2: Konfigürasyon hatası
ÇÖZÜM:
# Konfigürasyon dosyasını kontrol et
redis-cli -a GymKod2024Redis! CONFIG GET "*"

# Syntax hatası varsa default config'e dön
$defaultConfig = "C:\Program Files\Redis\redis.windows.conf"
Copy-Item $defaultConfig "C:\Program Files\Redis\redis.windows-service.conf"

SORUN 3: Memory leak
ÇÖZÜM:
# Memory kullanımını kontrol et
redis-cli -a GymKod2024Redis! info memory

# Cache'leri temizle (dikkatli!)
redis-cli -a GymKod2024Redis! FLUSHALL

SORUN 4: Performance düşük
ÇÖZÜM:
# Slow log'u kontrol et
redis-cli -a GymKod2024Redis! SLOWLOG GET 10

# Key'lerin dağılımını kontrol et
redis-cli -a GymKod2024Redis! --bigkeys

================================================================
ADIM 11: BAŞARILI KURULUM KONTROLÜ
================================================================

11.1) Tüm kontroller:
# Service durumu
Get-Service Redis

# Redis bağlantısı
redis-cli -a GymKod2024Redis! ping

# Memory ayarları
redis-cli -a GymKod2024Redis! info memory

# Scheduled tasks
Get-ScheduledTask | Where-Object {$_.TaskName -like "*Redis*"}

11.2) .NET uygulaması test:
cd C:\gymproject\GymProjectBackend\WebAPI
dotnet run --environment canlı

# Cache endpoint'lerini test et

================================================================
SONUÇ
================================================================

✅ AVANTAJLAR:
- Daha az kaynak kullanır
- Windows Service olarak çalışır
- Sistem yeniden başladığında otomatik başlar
- Native Windows integration

❌ DEZAVANTAJLAR:
- Eski Redis sürümü (3.0.504)
- Container'lar kadar esnek değil
- Güncelleme daha zor

🎯 ÖNERİ:
Önce Docker Engine'i dene, başarısız olursa bu yöntemi kullan.

BAŞARILAR! 🚀
